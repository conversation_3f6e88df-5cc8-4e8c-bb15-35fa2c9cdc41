# 中国万年历主题设计

## 设计理念

本应用采用现代化的中国红主题配色，旨在营造传统文化氛围的同时保持现代审美。配色方案既体现了中国传统文化的深厚底蕴，又符合当代用户的视觉习惯。

## 主要配色

### 主色调 - 中国红系列
- **主要中国红**: `#E53935` - 应用的主色调，用于AppBar、选中状态等
- **深红色**: `#C62828` - 用于强调和错误状态
- **浅红色**: `#EF5350` - 用于暗色主题的主色调
- **柔和红色**: `#FFEBEE` - 用于背景和淡化效果

### 辅助色彩
- **金色点缀**: `#FF8F00` - 用于次要强调和装饰
- **现代灰色**: `#424242` - 用于文本和图标
- **浅灰背景**: `#FAFAFA` - 用于页面背景

## 主题特色

### 1. 双主题支持
- **亮色主题**: 以白色为背景，中国红为主色调
- **暗色主题**: 以深色为背景，浅红色为主色调
- 自动跟随系统主题设置

### 2. 日历特色设计
- **选中日期**: 使用中国红背景，白色文字，圆角设计
- **今日标记**: 使用中国红边框和淡化背景
- **周末标记**: 使用柔和的红色文字
- **农历文字**: 使用半透明的主题色，保持层次感

### 3. 现代化设计元素
- **圆角设计**: 统一使用8-20px圆角，营造现代感
- **阴影效果**: 选中日期添加柔和阴影，增强视觉层次
- **色彩渐变**: 使用透明度变化创造层次感
- **Material 3**: 采用最新的Material Design 3规范

## 文化内涵

### 中国红的象征意义
- **喜庆吉祥**: 红色在中国文化中象征着喜庆、吉祥和好运
- **传统文化**: 体现了深厚的中华文化底蕴
- **节庆氛围**: 适合春节、中秋等传统节日的使用场景

### 现代审美融合
- **简约设计**: 避免过于复杂的装饰，保持界面简洁
- **舒适阅读**: 合理的对比度确保文字清晰易读
- **国际化**: 配色方案既有中国特色又符合国际审美标准

## 使用指南

### 在代码中使用主题
```dart
// 应用主题
MaterialApp(
  theme: AppTheme.lightTheme,
  darkTheme: AppTheme.darkTheme,
  themeMode: ThemeMode.system,
  // ...
)

// 获取主题颜色
Theme.of(context).colorScheme.primary  // 中国红
Theme.of(context).colorScheme.secondary  // 金色
```

### 自定义组件配色
- 优先使用主题中定义的颜色
- 保持与整体设计风格的一致性
- 注意颜色的对比度和可访问性

## 设计原则

1. **文化传承**: 体现中国传统文化特色
2. **现代审美**: 符合当代设计趋势
3. **用户友好**: 确保良好的用户体验
4. **一致性**: 保持整个应用的视觉统一
5. **可访问性**: 确保所有用户都能舒适使用

## 未来扩展

- 可考虑添加节日主题变化
- 支持用户自定义主题色调
- 增加更多中国传统色彩选项
- 适配不同地区的文化偏好
