import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:tiny_chinese_lunar_calendar/calendar/view/calendar_page.dart';
import 'package:tiny_chinese_lunar_calendar/l10n/l10n.dart';

void main() {
  group('Outside Date Cell Tests', () {
    Widget createTestApp() {
      return MaterialApp(
        localizationsDelegates: AppLocalizations.localizationsDelegates,
        supportedLocales: AppLocalizations.supportedLocales,
        home: const CalendarPage(),
      );
    }

    testWidgets('Outside dates should have consistent size with current month dates', (tester) async {
      await tester.pumpWidget(createTestApp());
      await tester.pumpAndSettle();

      // 查找日历组件
      expect(find.byType(CalendarPage), findsOneWidget);

      // 验证没有渲染错误
      expect(tester.takeException(), isNull);

      // 这个测试主要验证应用能正常启动和渲染
      // 实际的视觉效果需要手动验证
    });

    testWidgets('Calendar should render without overflow errors', (tester) async {
      // 测试不同的窗口尺寸
      await tester.binding.setSurfaceSize(const Size(800, 600));
      
      await tester.pumpWidget(createTestApp());
      await tester.pumpAndSettle();

      // 验证没有渲染溢出错误
      expect(tester.takeException(), isNull);
      
      // 验证日历组件存在
      expect(find.byType(CalendarPage), findsOneWidget);
    });
  });
}
